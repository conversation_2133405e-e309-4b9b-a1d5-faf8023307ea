import re
from datetime import timed<PERSON><PERSON>
from unittest.mock import patch

import pytest
from boto3.dynamodb.conditions import Key
from botocore.exceptions import ClientError

from eyecue_things_api.context import Context
from eyecue_things_api.dynamodb import (
    delete_table_item,
    get_indoor_tracker_item,
    get_tracker_item,
    insert_table_item,
    query_table_parsed,
    scan_table_parsed,
    update_table_item,
)
from eyecue_things_api.exceptions import (
    ItemDeleteError,
    ItemExistsError,
    ItemUpdateError,
    ModelTypeError,
)
from eyecue_things_api.util import convert_floats_to_decimal
from tests.util import ServerFactory, TrackerFactory


def test_scan_empty_table(execution_context: Context) -> None:
    items = list(scan_table_parsed(execution_context.things_shadow_table))
    assert len(items) == 0


def test_scan_table(
    execution_context: Context,
    tracker_factory: TrackerFactory,
    server_factory: ServerFactory,
) -> None:
    tracker = tracker_factory.create_tracker()
    server = server_factory.create_server()
    insert_table_item(table=execution_context.things_shadow_table, item=tracker)
    insert_table_item(table=execution_context.things_shadow_table, item=server)

    items = list(
        scan_table_parsed(
            execution_context.things_shadow_table,
            Limit=1,  # set Limit to 1 to test pagination
        )
    )
    assert len(items) == 2  # noqa: PLR2004
    assert items[0] == server
    assert items[1] == tracker


def test_query_table(
    execution_context: Context,
    tracker_factory: TrackerFactory,
    server_factory: ServerFactory,
) -> None:
    tracker = tracker_factory.create_tracker()
    server = server_factory.create_server()
    insert_table_item(table=execution_context.things_shadow_table, item=tracker)
    insert_table_item(table=execution_context.things_shadow_table, item=server)

    items = list(
        query_table_parsed(
            execution_context.things_shadow_table,
            KeyConditionExpression=Key("site_id").eq("fm-tst-aus-0318"),
            Limit=1,  # set Limit to 1 to test pagination
        )
    )
    assert len(items) == 2  # noqa: PLR2004
    assert items[0] == server
    assert items[1] == tracker


def test_insert_table_item_already_exists(
    execution_context: Context,
    tracker_factory: TrackerFactory,
) -> None:
    tracker = tracker_factory.create_tracker()
    insert_table_item(table=execution_context.things_shadow_table, item=tracker)

    # Ensure the item was inserted
    items = list(scan_table_parsed(execution_context.things_shadow_table))
    assert len(items) == 1
    assert items[0] == tracker

    with patch("eyecue_things_api.dynamodb.datetime") as mock_datetime:
        # Force the last_updated_timestamp to not change
        mock_datetime.now.return_value = tracker.last_updated_timestamp
        with pytest.raises(
            ItemExistsError,
            match=(
                "Item with site_id=fm-tst-aus-0318 and "
                "camera_id=eyeq-tracker-fm-tst-aus-0318-camera001 already exists"
            ),
        ):
            insert_table_item(table=execution_context.things_shadow_table, item=tracker)

    # Ensure the item was not inserted
    items = list(scan_table_parsed(execution_context.things_shadow_table))
    assert len(items) == 1
    assert items[0] == tracker


def test_insert_table_item_other_error(
    execution_context: Context,
    tracker_factory: TrackerFactory,
) -> None:
    large_string = "X" * 400 * 1024  # Maximum allowed size is 400 KB
    tracker = tracker_factory.create_tracker(author_name=large_string)

    with pytest.raises(
        ClientError,
        match=re.escape(
            "An error occurred (ValidationException) when calling the PutItem "
            "operation: Item size has exceeded the maximum allowed size"
        ),
    ):
        insert_table_item(table=execution_context.things_shadow_table, item=tracker)


def test_delete_table_item(
    execution_context: Context,
    tracker_factory: TrackerFactory,
) -> None:
    tracker = tracker_factory.create_tracker()
    insert_table_item(table=execution_context.things_shadow_table, item=tracker)

    # Ensure the item was inserted
    items = list(scan_table_parsed(execution_context.things_shadow_table))
    assert len(items) == 1
    assert items[0] == tracker

    delete_table_item(
        table=execution_context.things_shadow_table,
        site_id=tracker.site_id,
        camera_id=tracker.camera_id,
    )

    # Ensure the item was deleted
    items = list(scan_table_parsed(execution_context.things_shadow_table))
    assert len(items) == 0


def test_delete_table_item_doesnt_exist(
    execution_context: Context,
    tracker_factory: TrackerFactory,
) -> None:
    tracker = tracker_factory.create_tracker()
    with pytest.raises(
        ItemDeleteError,
        match=(
            "Unable to delete item with site_id=.* and camera_id=.*\\. "
            "Has the item been deleted already\\?"
        ),
    ):
        delete_table_item(
            table=execution_context.things_shadow_table,
            site_id=tracker.site_id,
            camera_id=tracker.camera_id,
        )


def test_delete_table_item_error(
    execution_context: Context,
    tracker_factory: TrackerFactory,
) -> None:
    tracker = tracker_factory.create_tracker()

    # Set the table name to an invalid value to force an error
    table = execution_context.dynamodb.Table("invalid-table-name")

    with pytest.raises(
        ClientError,
        match=re.escape(
            "An error occurred (ResourceNotFoundException) when calling the "
            "DeleteItem operation: Cannot do operations on a non-existent table"
        ),
    ):
        delete_table_item(
            table=table,
            site_id=tracker.site_id,
            camera_id=tracker.camera_id,
        )


def test_update_table_item_doesnt_exist(
    execution_context: Context,
    tracker_factory: TrackerFactory,
) -> None:
    tracker = tracker_factory.create_tracker()

    with pytest.raises(
        ItemUpdateError,
        match=(
            "Unable to update item with site_id=.*, camera_id=.*, "
            "last_updated_timestamp=.*. Either the item does not exist or it has been "
            "updated since it was last read."
        ),
    ):
        update_table_item(table=execution_context.things_shadow_table, item=tracker)


def test_update_table_item_success(
    execution_context: Context,
    tracker_factory: TrackerFactory,
) -> None:
    tracker = tracker_factory.create_tracker(author_name="<EMAIL>")
    insert_table_item(table=execution_context.things_shadow_table, item=tracker)

    # Update the item
    tracker.author_name = "<EMAIL>"
    update_table_item(table=execution_context.things_shadow_table, item=tracker)

    # Ensure the item was updated
    database_tracker = get_tracker_item(
        table=execution_context.things_shadow_table,
        site_id=tracker.site_id,
        tracker_id=tracker.camera_id,
    )
    assert database_tracker.author_name == "<EMAIL>"


def test_update_table_item_success_indoor(
    execution_context: Context,
    tracker_factory: TrackerFactory,
) -> None:
    tracker = tracker_factory.create_tracker_indoor(author_name="<EMAIL>")
    insert_table_item(table=execution_context.things_shadow_table, item=tracker)

    # Update the item
    tracker.author_name = "<EMAIL>"
    update_table_item(table=execution_context.things_shadow_table, item=tracker)

    # Ensure the item was updated
    database_tracker = get_indoor_tracker_item(
        table=execution_context.things_shadow_table,
        site_id=tracker.site_id,
        tracker_id=tracker.camera_id,
    )
    assert database_tracker.author_name == "<EMAIL>"


def test_update_table_item_error_indoor(
    execution_context: Context,
    tracker_factory: TrackerFactory,
) -> None:
    tracker = tracker_factory.create_tracker(author_name="<EMAIL>")
    insert_table_item(table=execution_context.things_shadow_table, item=tracker)

    # Ensure the item was updated
    # Expecting a ModelTypeError to be raised
    with pytest.raises(ModelTypeError, match="Expected item of type IndoorTracker"):
        get_indoor_tracker_item(
            table=execution_context.things_shadow_table,
            site_id=tracker.site_id,
            tracker_id=tracker.camera_id,
        )


def test_update_table_item_error_1(
    execution_context: Context,
    tracker_factory: TrackerFactory,
) -> None:
    tracker = tracker_factory.create_tracker(
        last_updated_timestamp="2021-01-01T00:00:01+00:00"
    )
    with patch("eyecue_things_api.dynamodb.datetime") as mock_datetime:
        # Force the last_updated_timestamp to not change
        mock_datetime.now.return_value = tracker.last_updated_timestamp
        insert_table_item(table=execution_context.things_shadow_table, item=tracker)

    # Make it updated in the past
    tracker.last_updated_timestamp -= timedelta(seconds=1)

    with pytest.raises(
        ItemUpdateError,
        match=(
            "Unable to update item with site_id=.*, camera_id=.*, "
            "last_updated_timestamp=2021-01-01T00\\:00\\:00\\+00\\:00. Either the item "
            "does not exist or it has been updated since it was last read."
        ),
    ):
        update_table_item(table=execution_context.things_shadow_table, item=tracker)


def test_update_table_item_error_2(
    execution_context: Context,
    tracker_factory: TrackerFactory,
) -> None:
    tracker = tracker_factory.create_tracker(
        last_updated_timestamp="2021-01-01T00:00:00+00:00"
    )
    with patch("eyecue_things_api.dynamodb.datetime") as mock_datetime:
        # Force the last_updated_timestamp to not change
        mock_datetime.now.return_value = tracker.last_updated_timestamp
        insert_table_item(table=execution_context.things_shadow_table, item=tracker)

    # Make it updated in the future and update the item
    tracker.last_updated_timestamp += timedelta(seconds=1)
    item = tracker.model_dump(mode="json")
    item = convert_floats_to_decimal(item)
    execution_context.things_shadow_table.put_item(Item=item)
    tracker.last_updated_timestamp -= timedelta(seconds=1)

    with pytest.raises(
        ItemUpdateError,
        match=(
            "Unable to update item with site_id=.*, camera_id=.*, "
            "last_updated_timestamp=2021-01-01T00\\:00\\:00\\+00\\:00. Either the item "
            "does not exist or it has been updated since it was last read."
        ),
    ):
        update_table_item(table=execution_context.things_shadow_table, item=tracker)


def test_update_table_item_error(
    execution_context: Context,
    tracker_factory: TrackerFactory,
) -> None:
    large_string = "X" * 400 * 1024  # Maximum allowed size is 400 KB
    tracker = tracker_factory.create_tracker(author_name=large_string)

    with pytest.raises(
        ClientError,
        match=re.escape(
            "An error occurred (ValidationException) when calling the PutItem "
            "operation: Item size has exceeded the maximum allowed size"
        ),
    ):
        update_table_item(table=execution_context.things_shadow_table, item=tracker)
