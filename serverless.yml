service: eyecue-things-api
useDotenv: true

plugins:
  - serverless-better-credentials
  - serverless-python-requirements

params:
  default:
    thingsShadowTableName: ${env:THINGS_SHADOW_TABLE_NAME, 'eyecue-things-shadow'}
    powertoolsServiceName: eyecue-things-api
    powertoolsLogLevel: INFO
  dev:
    powertoolsLogLevel: DEBUG

provider:
  name: aws
  runtime: python3.12
  region: ${env:AWS_REGION}
  deploymentMethod: direct
  logRetentionInDays: 365
  versionFunctions: false
  stage: ${env:ENVIRONMENT}
  stackTags: &stackTags
    Environment: ${env:ENVIRONMENT}
    Product: Eyecue
    Customer: ${env:ORGANIZATION}
    Terraform: "False"
    Stack: CV
    Serverless: "True"
    Application: ${self:service}
    Squad: Core
    Customer Facing: "False"
    System: ${self:provider.runtime}
  tags: *stackTags
  environment:
    THINGS_SHADOW_TABLE_NAME: ${param:thingsShadowTableName}
    POWERTOOLS_SERVICE_NAME: ${param:powertoolsServiceName}
    POWERTOOLS_LOG_LEVEL: ${param:powertoolsLogLevel}
  deploymentBucket:
    blockPublicAccess: true

  iam:
    role:
      tags: *stackTags
      statements:
        - Effect: "Allow"
          Action:
            - "dynamodb:Scan"
            - "dynamodb:Query"
            - "dynamodb:PutItem"
            - "dynamodb:GetItem"
            - "dynamodb:BatchGetItem"
            - "dynamodb:UpdateItem"
            - "dynamodb:DeleteItem"
          Resource: "arn:aws:dynamodb:${aws:region}:${aws:accountId}:table/${param:thingsShadowTableName}"

package:
  patterns:
    - "!./**"
    - eyecue_things_api/**
    - functions/**

functions:
  authorizer:
    handler: functions/auth/authorizer.handler
    runtime: nodejs18.x
    package:
      individually: true
      include:
        - functions/auth/**
    environment:
      AUTH0_DOMAIN: ${ssm:/auth0/domain}
      AUTH0_AUDIENCE: ${ssm:/auth0/audience}

  tracker-create:
    handler: functions.tracker.tracker_create.lambda_handler

  tracker-get:
    handler: functions.tracker.tracker_get.lambda_handler

  tracker-list:
    handler: functions.tracker.tracker_list.lambda_handler

  tracker-update:
    handler: functions.tracker.tracker_update.lambda_handler

  tracker-delete:
    handler: functions.tracker.tracker_delete.lambda_handler

  tracker-camera-get:
    handler: functions.tracker.camera.camera_get.lambda_handler

  roi-get-schema:
    handler: functions.tracker.roi_get_schema.lambda_handler
  
  roi-create:
    handler: functions.tracker.roi_create.lambda_handler
  
  roi-delete:
    handler: functions.tracker.roi_delete.lambda_handler
  
  roi-update:
    handler: functions.tracker.roi_update.lambda_handler
  
  detector-get-schema:
    handler: functions.tracker.detector_get_schema.lambda_handler
  
  stream-get-schema:
    handler: functions.tracker.stream_get_schema.lambda_handler
  
  tracker-get-schema:
    handler: functions.tracker.tracker_get_schema.lambda_handler
  
  server-get-schema:
    handler: functions.server.server_get_schema.lambda_handler

  server-create:
    handler: functions.server.server_create.lambda_handler

  server-get:
    handler: functions.server.server_get.lambda_handler

  server-update:
    handler: functions.server.server_update.lambda_handler

  server-delete:
    handler: functions.server.server_delete.lambda_handler

  indoor-create:
    handler: functions.indoor.indoor_create.lambda_handler
  
  indoor-delete:
    handler: functions.indoor.indoor_delete.lambda_handler
  
  indoor-detector-get-schema:
    handler: functions.indoor.indoor_detector_get_schema.lambda_handler
  
  indoor-tracker-get-schema:
    handler: functions.indoor.indoor_tracker_get_schema.lambda_handler
  
  indoor-get:
    handler: functions.indoor.indoor_get.lambda_handler
  
  indoor-list:
    handler: functions.indoor.indoor_list.lambda_handler
  
  indoor-roi-get-schema:
    handler: functions.indoor.indoor_roi_get_schema.lambda_handler
  
  indoor-stream-get-schema:
    handler: functions.indoor.indoor_stream_get_schema.lambda_handler
  
  indoor-update:
    handler: functions.indoor.indoor_update.lambda_handler
  
  indoor-roi-create:
    handler: functions.indoor.indoor_roi_create.lambda_handler
  
  indoor-roi-delete:
    handler: functions.indoor.indoor_roi_delete.lambda_handler
  
  indoor-roi-update:
    handler: functions.indoor.indoor_roi_update.lambda_handler
  
  indoor-camera-get:
    handler: functions.indoor.camera.indoor_camera_get.lambda_handler
