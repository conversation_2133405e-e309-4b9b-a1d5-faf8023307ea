import uuid as uuid_lib
from datetime import datetime

from eyecue_things_api.models import ThingShadowDiscriminator
from eyecue_things_api.models.types import CameraId, SiteId


class EyecueThingsApiError(Exception):
    """Base class for all exceptions in the eyecue-things-api package."""


class ItemExistsError(EyecueThingsApiError):
    """Raised when an item already exists in the database."""

    def __init__(self, site_id: SiteId, camera_id: CameraId) -> None:
        self.message = (
            f"Item with site_id={site_id} and camera_id={camera_id} already exists"
        )
        super().__init__(self.message)


class ItemNotFoundError(EyecueThingsApiError):
    """Raised when an item is not found in the database."""

    def __init__(self, site_id: SiteId, camera_id: CameraId) -> None:
        self.message = (
            f"Item with site_id={site_id} and camera_id={camera_id} does not exist"
        )
        super().__init__(self.message)


class ROINotFoundError(EyecueThingsApiError):
    """Raised when a ROI is not found in the database."""

    def __init__(
        self, site_id: SiteId, camera_id: CameraId, roi_uuid: uuid_lib.UUID
    ) -> None:
        self.message = (
            f"ROI with UUID {roi_uuid} does not exist in "
            f"site_id={site_id} and camera_id={camera_id}"
        )
        super().__init__(self.message)


class ItemUpdateError(EyecueThingsApiError):
    """Raised when trying to update an item."""

    def __init__(
        self,
        site_id: SiteId,
        camera_id: CameraId,
        item_timestamp: datetime,
    ) -> None:
        self.message = (
            f"Unable to update item with site_id={site_id}, camera_id={camera_id}, "
            f"last_updated_timestamp={item_timestamp.isoformat()}. Either the item "
            "does not exist or it has been updated since it was last read."
        )
        super().__init__(self.message)


class ItemDeleteError(EyecueThingsApiError):
    """Raised when trying to delete an item."""

    def __init__(self, site_id: SiteId, camera_id: CameraId) -> None:
        self.message = (
            f"Unable to delete item with site_id={site_id} and camera_id={camera_id}. "
            "Has the item been deleted already?"
        )
        super().__init__(self.message)


class ModelTypeError(EyecueThingsApiError):
    """Raised when the item is not of the expected type."""

    def __init__(
        self,
        expected_type: type[ThingShadowDiscriminator],
        actual_type: type[ThingShadowDiscriminator],
    ) -> None:
        self.message = (
            f"Expected item of type {expected_type.__name__}, "
            f"but got item of type {actual_type.__name__}"
        )
        super().__init__(self.message)
