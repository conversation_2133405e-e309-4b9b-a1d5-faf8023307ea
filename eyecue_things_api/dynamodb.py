from collections.abc import Iterable
from datetime import UTC, datetime
from typing import Any, TypeVar, Unpack

from botocore.exceptions import ClientError
from mypy_boto3_dynamodb.service_resource import Table
from mypy_boto3_dynamodb.type_defs import (
    QueryInputTableQueryTypeDef,
    ScanInputTableScanTypeDef,
)
from pydantic import TypeAdapter

from eyecue_things_api.boto3_type_defs import (
    DeleteItemInputTableDeleteItemTypeDef,
    GetItemInputTableGetItemTypeDef,
    PutItemInputTablePutItemTypeDef,
)
from eyecue_things_api.exceptions import (
    ItemDeleteError,
    ItemExistsError,
    ItemNotFoundError,
    ItemUpdateError,
    ModelTypeError,
)
from eyecue_things_api.models import Server, ThingShadowDiscriminator, Tracker
from eyecue_things_api.models.tracker.indoor_tracker import IndoorTracker
from eyecue_things_api.models.types import <PERSON>Id, ServerId, SiteId, TrackerId
from eyecue_things_api.util import convert_floats_to_decimal

T = TypeVar("T", bound=ThingShadowDiscriminator)
adapter: TypeAdapter[ThingShadowDiscriminator] = TypeAdapter(ThingShadowDiscriminator)


def query_table(
    table: Table,
    **kwargs: Unpack[QueryInputTableQueryTypeDef],
) -> Iterable[dict[str, Any]]:
    """Query a DynamoDB table and return all items.

    Args:
        table (Table): The DynamoDB table to query
        **kwargs: Additional arguments to pass to the query method

    Returns:
        Iterable[dict[str, Any]]: An iterable of items in the table

    """
    response = table.query(**kwargs)
    yield from response["Items"]

    while "LastEvaluatedKey" in response:
        kwargs["ExclusiveStartKey"] = response["LastEvaluatedKey"]
        response = table.query(**kwargs)
        yield from response["Items"]


def query_table_parsed(
    table: Table,
    **kwargs: Unpack[QueryInputTableQueryTypeDef],
) -> Iterable[ThingShadowDiscriminator]:
    """Query a DynamoDB table and return all items, parsed as ThingShadowDiscriminator.

    Args:
        table (Table): The DynamoDB table to query
        **kwargs: Additional arguments to pass to the query method

    Returns:
        Iterable[ThingShadowDiscriminator]: An iterable of items in the table.

    """
    for item in query_table(table, **kwargs):
        yield adapter.validate_python(item)


def scan_table(
    table: Table,
    **kwargs: Unpack[ScanInputTableScanTypeDef],
) -> Iterable[dict[str, Any]]:
    """Scan a DynamoDB table and return all items.

    Args:
        table (Table): The DynamoDB table to scan
        **kwargs: Additional arguments to pass to the scan method

    Returns:
        Iterable[dict[str, Any]]: An iterable of items in the table

    """
    response = table.scan(**kwargs)
    yield from response["Items"]

    while "LastEvaluatedKey" in response:
        kwargs["ExclusiveStartKey"] = response["LastEvaluatedKey"]
        response = table.scan(**kwargs)
        yield from response["Items"]


def scan_table_parsed(
    table: Table,
    **kwargs: Unpack[ScanInputTableScanTypeDef],
) -> Iterable[ThingShadowDiscriminator]:
    """Scan a DynamoDB table and return all items, parsed as ThingShadowDiscriminator.

    Args:
        table (Table): The DynamoDB table to scan
        **kwargs: Additional arguments to pass to the scan method

    Returns:
        Iterable[ThingShadowDiscriminator]: An iterable of items in the table

    """
    for item in scan_table(table, **kwargs):
        yield adapter.validate_python(item)


def get_table_item(
    table: Table,
    site_id: SiteId,
    camera_id: CameraId,
    **kwargs: Unpack[GetItemInputTableGetItemTypeDef],
) -> dict[str, Any]:
    """Get a single item from a DynamoDB table.

    Args:
        table (Table): The DynamoDB table to get the item from
        site_id (SiteId): The site_id of the item
        camera_id (CameraId): The camera_id of the item
        **kwargs: Additional arguments to pass to the get_item method

    Returns:
        dict[str, Any]: The item from the table

    Raises:
        ItemNotFoundError: If the item is not found

    """
    response = table.get_item(
        Key={
            "site_id": site_id,
            "camera_id": camera_id,
        },
        **kwargs,
    )
    try:
        return response["Item"]
    except KeyError as e:
        raise ItemNotFoundError(site_id, camera_id) from e


def get_table_item_parsed(
    table: Table,
    site_id: SiteId,
    camera_id: CameraId,
    **kwargs: Unpack[GetItemInputTableGetItemTypeDef],
) -> ThingShadowDiscriminator:
    """Get a single item from the DynamoDB table, parsed as ThingShadowDiscriminator.

    Args:
        table (Table): The DynamoDB table to get the item from
        site_id (SiteId): The site_id of the item
        camera_id (CameraId): The camera_id of the item
        **kwargs: Additional arguments to pass to the get_item method

    Returns:
        ThingShadowDiscriminator: The item from the table

    Raises:
        ItemNotFoundError: If the item does not exist

    """
    return adapter.validate_python(get_table_item(table, site_id, camera_id, **kwargs))


def get_server_item(
    table: Table,
    site_id: SiteId,
    server_id: ServerId,
    **kwargs: Unpack[GetItemInputTableGetItemTypeDef],
) -> Server:
    """Get a Server item from the DynamoDB table.

    Args:
        table (Table): The DynamoDB table to get the item from
        site_id (SiteId): The site_id of the item
        server_id (ServerId): The server_id of the item
        **kwargs: Additional arguments to pass to the get_item method

    Returns:
        Server: The Server item

    Raises:
        ModelTypeError: If the item is not of type Server
        ItemNotFoundError: If the item does not exist

    """
    response = get_table_item_parsed(
        table,
        site_id,
        server_id,
        **kwargs,
    )

    if not isinstance(response, Server):
        raise ModelTypeError(Server, type(response))

    return response


def get_tracker_item(
    table: Table,
    site_id: SiteId,
    tracker_id: TrackerId,
    **kwargs: Unpack[GetItemInputTableGetItemTypeDef],
) -> Tracker:
    """Get a Tracker item from the DynamoDB table.

    Args:
        table (Table): The DynamoDB table to get the item from
        site_id (SiteId): The site_id of the item
        tracker_id (TrackerId): The tracker_id of the item
        **kwargs: Additional arguments to pass to the get_item method

    Returns:
        Tracker: The Tracker item

    Raises:
        ModelTypeError: If the item is not of type Tracker
        ItemNotFoundError: If the item does not exist

    """
    response = get_table_item_parsed(
        table,
        site_id,
        tracker_id,
        **kwargs,
    )

    if not isinstance(response, Tracker):
        raise ModelTypeError(Tracker, type(response))

    return response


def get_indoor_tracker_item(
    table: Table,
    site_id: SiteId,
    tracker_id: TrackerId,
    **kwargs: Unpack[GetItemInputTableGetItemTypeDef],
) -> IndoorTracker:
    """Get a IndoorTracker item from the DynamoDB table.

    Args:
        table (Table): The DynamoDB table to get the item from
        site_id (SiteId): The site_id of the item
        tracker_id (TrackerId): The tracker_id of the item
        **kwargs: Additional arguments to pass to the get_item method

    Returns:
        IndoorTracker: The IndoorTracker item

    Raises:
        ModelTypeError: If the item is not of type IndoorTracker
        ItemNotFoundError: If the item does not exist

    """
    response = get_table_item_parsed(
        table,
        site_id,
        tracker_id,
        **kwargs,
    )

    if not isinstance(response, IndoorTracker):
        raise ModelTypeError(IndoorTracker, type(response))

    return response


def insert_table_item(
    table: Table,
    item: T,
    **kwargs: Unpack[PutItemInputTablePutItemTypeDef],
) -> T:
    """Insert an item into a DynamoDB table.

    Args:
        table (Table): The DynamoDB table to insert the item into
        item (ThingShadowDiscriminator): The item to insert
        **kwargs (Unpack[PutItemInputTablePutItemTypeDef]): Additional arguments
            to pass to the put_item method

    Returns:
        ThingShadowDiscriminator: The inserted item.

    Raises:
        ItemExistsError: If the item already exists in the database

    """
    kwargs["ConditionExpression"] = (
        "attribute_not_exists(site_id) AND attribute_not_exists(camera_id)"
    )

    try:
        item.last_updated_timestamp = datetime.now(UTC)
        item_json = item.model_dump(mode="json")
        item_json = convert_floats_to_decimal(item_json)
        table.put_item(Item=item_json, **kwargs)
    except ClientError as e:
        if e.response["Error"]["Code"] == "ConditionalCheckFailedException":
            raise ItemExistsError(item.site_id, item.camera_id) from e
        raise e

    return item


def update_table_item(
    table: Table,
    item: T,
    **kwargs: Unpack[PutItemInputTablePutItemTypeDef],
) -> T:
    """Update an item in a DynamoDB table.

    Args:
        table (Table): The DynamoDB table to update the item in
        item (ThingShadowDiscriminator): The item to update
        **kwargs (Unpack[PutItemInputTablePutItemTypeDef]): Additional arguments
            to pass to the put_item method

    Returns:
        ThingShadowDiscriminator: The updated item

    Raises:
        ItemUpdateError: If the item has been updated since it was last read or
            if the item does not exist

    """
    last_updated_timestamp_before = item.last_updated_timestamp
    kwargs["ConditionExpression"] = (
        "attribute_exists(site_id) AND attribute_exists(camera_id) "
        "and last_updated_timestamp = :updated_timestamp"
    )
    kwargs["ExpressionAttributeValues"] = {
        ":updated_timestamp": last_updated_timestamp_before.isoformat(),
    }

    try:
        item.last_updated_timestamp = datetime.now(UTC)
        item_json = item.model_dump(mode="json")
        item_json = convert_floats_to_decimal(item_json)
        table.put_item(Item=item_json, **kwargs)
    except ClientError as e:
        if e.response["Error"]["Code"] == "ConditionalCheckFailedException":
            raise ItemUpdateError(
                item.site_id,
                item.camera_id,
                last_updated_timestamp_before,
            ) from e

        raise e

    return item


def delete_table_item(
    table: Table,
    site_id: SiteId,
    camera_id: CameraId,
    **kwargs: Unpack[DeleteItemInputTableDeleteItemTypeDef],
) -> None:
    """Delete an item from a DynamoDB table.

    Args:
        table (Table): The DynamoDB table to delete the item from
        site_id (SiteId): The site_id of the item
        camera_id (CameraId): The camera_id of the item
        **kwargs: Additional arguments to pass to the delete_item method

    Raises:
        ItemDeleteError: If the item has already been deleted

    """
    kwargs["ConditionExpression"] = (
        "attribute_exists(site_id) AND attribute_exists(camera_id)"
    )

    try:
        table.delete_item(
            Key={
                "site_id": site_id,
                "camera_id": camera_id,
            },
            **kwargs,
        )
    except ClientError as e:
        if e.response["Error"]["Code"] == "ConditionalCheckFailedException":
            raise ItemDeleteError(site_id, camera_id) from e
        raise e
