import os
from dataclasses import dataclass, field

import boto3
from mypy_boto3_dynamodb.service_resource import DynamoDBServiceResource, Table


@dataclass
class Context:
    boto3_session: boto3.Session = field(default_factory=boto3.Session)
    things_shadow_table_name: str = field(
        default_factory=lambda: os.getenv(
            "THINGS_SHADOW_TABLE_NAME", "eyecue-things-shadow"
        )
    )

    dynamodb: DynamoDBServiceResource = field(init=False)
    things_shadow_table: Table = field(init=False)

    _endpoint_url: str | None = field(default=None)  # only used for testing

    def __post_init__(self) -> None:
        self.dynamodb = self.boto3_session.resource(
            "dynamodb", endpoint_url=self._endpoint_url
        )
        self.things_shadow_table = self.dynamodb.Table(self.things_shadow_table_name)
