from typing import Annotated, Any

from pydantic import Discriminator, Tag

from eyecue_things_api.models.server import Server
from eyecue_things_api.models.tracker import Tracker
from eyecue_things_api.models.tracker.indoor_tracker import IndoorTracker


def get_discriminator_value(v: Any) -> str | None:
    """Return a discriminator value for the item.

    The returned value must be the name of the tag that the value is annotated with.

    Args:
        v (Any): The value to get the discriminator value for.

    Returns:
        str | None: The discriminator value for the EyecueThingsShadowConfig model.
            Returns None if the discriminator value is not found or not valid.

    """
    try:
        camera_id = v["camera_id"]
        service = v.get("service", None)
    except KeyError:
        return None

    if not isinstance(camera_id, str):
        return None

    if service == "Indoor":
        return "indoor"

    if "eyeq-tracker" in camera_id:
        return "tracker"

    if "eyeq-server" in camera_id:
        return "server"

    return None


ThingShadowDiscriminator = Annotated[
    Annotated[Server, Tag("server")]
    | Annotated[Tracker, Tag("tracker")]
    | Annotated[IndoorTracker, Tag("indoor")],
    Discriminator(get_discriminator_value),
]
