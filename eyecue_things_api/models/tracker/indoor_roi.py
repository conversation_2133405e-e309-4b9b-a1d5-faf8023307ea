import uuid as uuid_lib
from typing import Any

from pydantic import BaseModel, Field

from eyecue_things_api.model_config import model_config
from eyecue_things_api.models.types import (
    Coordinates,
    IndoorRoiType,
)


class IndoorRoi(BaseModel):
    model_config = model_config

    coordinates: list[Coordinates] = Field(  # type: ignore[valid-type]
        description="The coordinates of the ROI.",
        examples=[[[0.0, 0.0], [0.0, 1.0], [1.0, 1.0], [1.0, 0.0]]],
    )
    extra: dict[str, Any] | None = Field(
        description="Extra parameters that can be used by the ROI.",
        default=None,
    )
    group_id: uuid_lib.UUID | None = Field(
        description="UUID of the ROI group.",
        default=None,
    )
    group_name: str | None = Field(
        description="Name of the ROI group.",
        default=None,
    )
    id: str = Field(
        description="The id of the ROI. Must be unique.", examples=["deliver"]
    )
    type: IndoorRoiType = Field(
        description="The type of the ROI.", examples=["workstation|workstation_stats"]
    )
    uuid: uuid_lib.UUID = Field(
        description="UUID for the ROI", default_factory=uuid_lib.uuid4
    )
