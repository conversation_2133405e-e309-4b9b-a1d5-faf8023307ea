import uuid as uuid_lib
from typing import Any

from pydantic import BaseModel, Field, field_validator, model_validator

from eyecue_things_api.models.base import ThingEntityModel
from eyecue_things_api.models.tracker.camera import Camera
from eyecue_things_api.models.tracker.detector import Detector
from eyecue_things_api.models.tracker.roi import Roi
from eyecue_things_api.models.types import Service, TrackerId


class Config(BaseModel):
    log_level_debug: bool | None = Field(
        description="Whether to log debug messages or not.",
        default=False,
    )
    log_output_path: str | None = Field(
        description="The path where the logs will be saved.",
        examples=["/tmp/eyeq-tracker-logs"],
        default="logs.log",
    )


class Tracking(BaseModel):
    max_age: int | None = Field(default=6, ge=1)
    min_hits: int | None = Field(default=3, ge=1)
    name: str | None = Field(
        description="The name of the tracker.",
        examples=["tracker-name"],
        # Gabriel: almost sure this is not used for anything. Kept for now.
        deprecated=True,
        default=None,
    )
    threshold: float | None = Field(default=0.5, ge=0.0, le=1.0)


class Tracker(ThingEntityModel):
    camera: Camera = Field(description="The configuration options for the camera.")
    camera_id: TrackerId = Field(
        description="The id of the tracker.",
        examples=[
            "eyeq-tracker-fm-mcd-aus-1305-camera15",
            "eyeq-tracker-fm-cfa-usa-01490-camera148",
        ],
    )
    config: Config | None = Field(
        description="The configuration options for the application.",
        default_factory=Config,
    )
    detector: Detector = Field(
        description="The configuration options for the detector.",
        default_factory=Detector,
    )
    rois: list[Roi] = Field(
        description="The configuration options for the ROIs.", default_factory=list
    )
    service: Service = Field(
        description="The service that is running on camera (eyecue or indoor).",
        default="Drive-Thru",
    )
    tracking: Tracking | None = Field(
        description="The configuration options for the tracking.",
        default_factory=Tracking,
    )
    uuid: uuid_lib.UUID = Field(
        description="UUID for the tracker object", default_factory=uuid_lib.uuid4
    )

    # mypy: ignore-errors
    @model_validator(mode="before")
    @classmethod
    def set_camera_name(cls, values: dict[str, Any]) -> dict[str, Any]:
        """Set camera.name using site_id and camera_id."""
        camera_data = values.get("camera")
        camera_id = values.get("camera_id")
        site_id = values.get("site_id")

        assert camera_data is not None
        assert camera_id is not None
        assert site_id is not None
        # Convert to Camera instance (it is a dict when root_validator is running)
        if not isinstance(camera_data, Camera):
            camera_data = Camera(**camera_data)

        camera_number = camera_id.split("-camera")[1]
        camera_name = f"{site_id}-{camera_number}"
        values["camera"] = camera_data.model_copy(update={"name": camera_name})
        return values

    @field_validator("rois", mode="after")
    @classmethod
    def validate_roi(cls, rois: list[Roi]) -> list[Roi]:
        """Validate that the ROI IDs are unique."""
        roi_ids = [roi.id for roi in rois]
        # Find duplicate ROI IDs. Sort it so that the error message is deterministic.
        duplicates = sorted(
            roi_id for roi_id in set(roi_ids) if roi_ids.count(roi_id) > 1
        )
        if duplicates:
            raise ValueError(f"Duplicate ROI IDs found: {', '.join(duplicates)}")

        return rois
