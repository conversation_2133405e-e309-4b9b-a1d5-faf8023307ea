from pathlib import Path

from pydantic import BaseModel, Field

from eyecue_things_api.models.types import AnyRtspUrl, MainFlowDirection, ThreeDigitStr


class Camera(BaseModel):
    camera_id: ThreeDigitStr = Field(
        description="The ID of the camera (must be a three-digit number).",
        examples=["016", "148"],
    )
    finish_camera: bool | None = Field(
        description="Whether this is a finish camera or not.", default=False
    )
    fps: int | None = Field(
        description="Max frames per second that the camera should track.",
        default=6,
        ge=1,
        alias="FPS",
    )
    main_flow_direction: MainFlowDirection | None = Field(
        description=(
            "The direction the vehicles are moving in "
            "the main flow of traffic. (Deprecated)"
        ),
        default=None,
        deprecated=True,
    )
    name: str | None = Field(
        description="Name of the camera.",
        examples=["fm-mcd-aus-1305-016", "fm-cfa-usa-01490-148"],
        default=None,
    )
    reverse_detection: bool | None = Field(
        description="Whether to detect reverse or not.", default=False
    )
    reverse_frames_threshold: int | None = Field(
        description=(
            "How many frames a car must be reversing to trigger a reverse status"
        ),
        default=3,
        ge=1,
    )
    s3_path: str | None = Field(
        description="Path to the s3 bucket image of this camera.",
        default=None,
    )
    source: AnyRtspUrl | Path = Field(
        description="RTPS Source of the camera.",
        examples=["rtsp://localhost:8554/0", "video.mp4"],
    )
    start_camera: bool | None = Field(
        description="Whether this is a start camera or not.", default=False
    )
