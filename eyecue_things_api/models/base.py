from datetime import UTC, datetime

from pydantic import (
    AwareDatetime,
    BaseModel,
    EmailStr,
    Field,
    SerializationInfo,
    field_serializer,
)

from eyecue_things_api.model_config import model_config
from eyecue_things_api.models.types import CameraId, SiteId


class ThingEntityModel(BaseModel):
    model_config = model_config
    author_name: EmailStr | str = Field(
        description="The name of the author of the last change.",
        examples=["<EMAIL>", "Person Name"],
    )
    camera_id: CameraId = Field(
        description="The id of the tracker",
        examples=[
            "eyeq-server-fm-mcd-aus-1305",
            "eyeq-tracker-fm-mcd-aus-1305-camera15",
        ],
    )
    last_updated_timestamp: AwareDatetime = Field(
        description="The timestamp of the last update.",
        default_factory=lambda: datetime.now(UTC),
    )
    site_id: SiteId = Field(
        description="The id of the site",
        examples=["fm-mcd-aus-1305", "fm-cfa-usa-01490"],
    )

    @field_serializer("last_updated_timestamp")
    def serialize_last_updated_timestamp(
        self,
        last_updated_timestamp: AwareDatetime,
        info: SerializationInfo,
    ) -> AwareDatetime | str:
        """Serialize the last_updated_timestamp to UTC timezone in ISO 8601 format."""
        if info.mode_is_json():
            return last_updated_timestamp.astimezone(UTC).isoformat()

        return last_updated_timestamp.astimezone(UTC)
