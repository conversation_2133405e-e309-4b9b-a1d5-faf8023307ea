from http import HTTPStatus
from typing import Any, Literal

from pydantic import (
    BaseModel,
    Field,
    SerializerFunctionWrapHandler,
    field_serializer,
    model_serializer,
)
from pydantic_core import ErrorDetails

from eyecue_things_api.model_config import model_config

##### Error Body #####


class ErrorBody(BaseModel):
    model_config = model_config

    message: str
    error: str


class ValidationErrorBody(ErrorBody):
    details: list[ErrorDetails]

    @field_serializer("details", when_used="json")
    def serialize_details(self, details: list[ErrorDetails]) -> list[ErrorDetails]:
        for detail in details:
            self._serialize_detail(detail)
        return details

    def _serialize_detail(self, detail: ErrorDetails) -> ErrorDetails:
        try:
            error = detail["ctx"]["error"]
        except KeyError:
            return detail

        if isinstance(error, ValueError):
            detail["ctx"]["error"] = str(error)

        return detail


##### Lambda Response #####


class LambdaResponse(BaseModel):
    """Base class for all lambda responses."""

    model_config = model_config

    status_code: int = Field(
        default=HTTPStatus.OK.value,
        ge=100,
        le=600,
    )
    body: Any

    @model_serializer(when_used="json", mode="wrap")
    def serialize_model(self, nxt: SerializerFunctionWrapHandler) -> Any:
        result = nxt(self)
        result["statusCode"] = self.status_code
        del result["status_code"]
        return result


##### Error Responses #####


class ErrorResponse(LambdaResponse):
    status_code: int = Field(
        default=HTTPStatus.BAD_REQUEST.value, ge=HTTPStatus.BAD_REQUEST.value
    )
    body: ErrorBody


class ValidationErrorResponse(ErrorResponse):
    status_code: Literal[422, 500] = Field(
        default=HTTPStatus.UNPROCESSABLE_ENTITY.value
    )
    body: ValidationErrorBody
